package com.stpl.tech.master.domain.model;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * Response DTO for bulk upload operations
 */
@Data
public class BulkEmployeeMappingUploadResponse {
    
    private int totalRecords;
    private int validRecords;
    private int invalidRecords;
    private int successfullyProcessed;
    private List<BulkUploadValidationError> validationErrors;
    private List<ExistingIds> existingIds;
    private String message;
    private boolean success;
    
    @Data
    public static class BulkUploadValidationError {
        private int rowNumber;
        private String empId;
        private String field;
        private String errorMessage;
        private String originalValue;
    }

    @Data
    public static class ExistingIds {
        private int rowNumber;
        private String empId;
        private Long mappingId;

        public ExistingIds(int rowNumber, @NotBlank(message = "Employee ID is required") String empId, Long mappingId) {
            this.rowNumber = rowNumber;
            this.empId = empId;
            this.mappingId = mappingId;
        }
    }
}
