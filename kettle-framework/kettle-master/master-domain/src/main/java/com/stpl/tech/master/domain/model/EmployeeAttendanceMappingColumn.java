package com.stpl.tech.master.domain.model;

public enum EmployeeAttendanceMappingColumn {
    // to string
    EMPLOYEE_ID("EMPLOYEE_ID"),
    EMPLOYEE_NAME("EMPLOYEE_NAME"),
    EMPLOYEE_CODE("EMPLOYEE_CODE"),
    MAPPING_TYPE("MAPPING_TYPE"),
    VALUE("VALUE"),
    UNIT_NAME("UNIT_NAME"),
    ELIGIBILITY_TYPE("ELIGIBILITY_TYPE"),
    STATUS("STATUS");

    private final String columnName;

    EmployeeAttendanceMappingColumn(String columnName) {
        this.columnName = columnName;
    }

    public String getColumnName() {
        return columnName;
    }
}
