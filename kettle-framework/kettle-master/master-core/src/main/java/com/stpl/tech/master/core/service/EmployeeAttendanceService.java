package com.stpl.tech.master.core.service;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.BulkEmployeeAttendanceUploadResponse;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingRequest;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingResponse;
import org.springframework.web.servlet.View;

import java.util.List;

/**
 * Service interface for Employee Eligibility Mapping operations
 */
public interface EmployeeAttendanceService {

    int saveEmployeeAttendanceMappingList(List<EmployeeEligibilityMappingRequest> requestList, String createdBy) throws DataUpdationException;

    /**
     * Save batch mappings from UI
     * @param requestList list of mapping requests
     * @param createdBy user who created the mappings
     * @return batch save response
     * @throws DataUpdationException if save fails
     */
    BulkEmployeeAttendanceUploadResponse saveEmployeeAttendanceBatchMappings(List<EmployeeEligibilityMappingRequest> requestList, String createdBy) throws DataUpdationException;

    /**
     * Get mappings by employee ID
     * @param empId employee ID
     * @return list of mappings
     */
    List<EmployeeEligibilityMappingResponse> getEligibilityAttendanceMappingsByEmpId(String empId);

    View prepareEmployeeMappingExcel(List<Long> empIds) throws Exception;

    /**
     * Download template for bulk upload
     * @return Excel template view
     * @throws Exception if template generation fails
     */
    View downloadEmployeeAttendanceBulkUploadTemplate() throws Exception;

    /**
     * Validate bulk upload data without saving
     * @param bulkData list of mapping requests
     * @return validation response with errors
     * @throws Exception if validation fails
     */
    BulkEmployeeAttendanceUploadResponse validateBulkUpload(List<EmployeeEligibilityMappingRequest> bulkData) throws Exception;

    /**
     * Process bulk upload data with backend validation and save to database
     * @param bulkData list of mapping requests
     * @param createdBy user who created the mappings
     * @return bulk upload response with validation results
     * @throws Exception if processing fails
     */
    BulkEmployeeAttendanceUploadResponse processBulkUpload(List<EmployeeEligibilityMappingRequest> bulkData, String createdBy) throws Exception;
}
