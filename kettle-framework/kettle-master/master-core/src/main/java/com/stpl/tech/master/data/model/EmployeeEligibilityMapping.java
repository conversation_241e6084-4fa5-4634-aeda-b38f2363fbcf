package com.stpl.tech.master.data.model;

import com.stpl.tech.master.domain.model.EligibilityType;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingType;
import com.stpl.tech.master.domain.model.SystemStatus;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

/**
 * JPA Entity for EMP_ELIGIBILITY_MAPPING table
 */
@Entity
@Table(name = "EMP_ELIGIBILITY_MAPPING")
@Data
public class EmployeeEligibilityMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "CREATED_AT", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @Column(name = "CREATED_BY", length = 255)
    private String createdBy;

    @Enumerated(EnumType.STRING)
    @Column(name = "ELIGIBILITY_TYPE" , columnDefinition = "ENUM('ATTENDANCE','APPROVAL')", nullable = false)
    private EligibilityType eligibilityType;

    @Column(name = "EMP_ID", length = 255, nullable = false)
    private String empId;

    @Enumerated(EnumType.STRING)
    @Column(name = "MAPPING_TYPE" , columnDefinition = "ENUM('UNIT','CITY','REGION')", nullable = false)
    private EmployeeEligibilityMappingType mappingType;

    @Enumerated(EnumType.STRING)
    @Column(name = "STATUS" , columnDefinition = "ENUM('ACTIVE','IN_ACTIVE')")
    private SystemStatus status;

    @Column(name = "UPDATED_AT", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    @Column(name = "UPDATED_BY", length = 255)
    private String updatedBy;

    @Column(name = "VALUE", length = 255, nullable = false)
    private String value;
    
}
