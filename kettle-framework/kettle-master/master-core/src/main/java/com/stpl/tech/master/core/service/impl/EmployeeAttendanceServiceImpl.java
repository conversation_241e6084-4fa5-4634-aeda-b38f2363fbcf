package com.stpl.tech.master.core.service.impl;

import com.stpl.tech.master.core.cache.EmployeeCache;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.EmployeeAttendanceService;
import com.stpl.tech.master.data.dao.EmployeeAttendanceDao;
import com.stpl.tech.master.data.model.EmployeeAttendance;
import com.stpl.tech.master.data.model.ExcelRequestData;
import com.stpl.tech.master.domain.model.*;
import java.util.ArrayList;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.View;
import com.stpl.tech.master.core.external.excel.GenericExcelManagementService;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Service implementation for Employee Eligibility Mapping operations
 */
@Service
@Transactional
public class EmployeeAttendanceServiceImpl implements EmployeeAttendanceService {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeAttendanceServiceImpl.class);

    @Autowired
    private EmployeeAttendanceDao employeeAttendanceDao;

    @Autowired
    private GenericExcelManagementService genericExcelManagementService;

    @Autowired
    private EmployeeCache empCache;

    @Autowired
    private MasterDataCache masterDataCache;


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public int saveEmployeeAttendanceMappingList(List<EmployeeEligibilityMappingRequest> requestList, String createdBy) throws DataUpdationException {
        LOG.info("Saving batch employee eligibility mappings, total records: {}", requestList.size());
        List<EmployeeAttendance> mappingsToInsert = new ArrayList<>();
        List<String> skipped = new ArrayList<>();
        int SuccessCount = 0;
        for (EmployeeEligibilityMappingRequest req : requestList) {
            try {
                // Check if mapping already exists
                List<EmployeeAttendance> existingEmpMapping = employeeAttendanceDao.findByEmpIdValueTypeAndStatus(
                        req.getEmpId(), req.getValue(), req.getMappingType(), req.getEligibilityType(), req.getStatus());

                if (!existingEmpMapping.isEmpty()) {
                    LOG.warn("Skipping duplicate mapping for empId={}, value={}, mappingType={}, eligibilityType={}, status={}",
                            req.getEmpId(), req.getValue(), req.getMappingType(), req.getEligibilityType(), req.getStatus());
                    skipped.add(req.getEmpId());
                    throw new DataUpdationException("Duplicate mapping found for empId=" + req.getEmpId());
                }

                // checking and updating the status
                List<EmployeeAttendance> existingStatusMapping = employeeAttendanceDao.findByEmpIdValueAndType(
                        req.getEmpId(), req.getValue(), req.getMappingType(), req.getEligibilityType());
                if(!existingStatusMapping.isEmpty()){
                    existingStatusMapping.get(0).setStatus(req.getStatus());
                    existingStatusMapping.get(0).setUpdatedBy(createdBy);
                    existingStatusMapping.get(0).setUpdatedAt(AppUtils.getCurrentTimestamp());
                    employeeAttendanceDao.update(existingStatusMapping.get(0));
                    SuccessCount++;
                }else {

                    // Build entity
                    EmployeeAttendance mapping = new EmployeeAttendance();
                    mapping.setEligibilityType(req.getEligibilityType());
                    mapping.setEmpId(req.getEmpId());
                    mapping.setMappingType(req.getMappingType());
                    mapping.setValue(req.getValue());
                    mapping.setCreatedBy(createdBy);
                    mapping.setUpdatedBy(createdBy);
                    mapping.setStatus(req.getStatus());
                    mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
                    mapping.setUpdatedAt(AppUtils.getCurrentTimestamp());

                    mappingsToInsert.add(mapping);
                    SuccessCount++;
                }
            } catch (Exception e) {
                LOG.error("Error processing request for empId={}", req.getEmpId(), e);
            }
        }
        LOG.info("Total Employee Attendance mappings to insert: {}", mappingsToInsert.size());
        if (!mappingsToInsert.isEmpty()) {
            employeeAttendanceDao.batchInsert(mappingsToInsert);

            List<EmployeeEligibilityMappingResponse> responses = mappingsToInsert.stream()
                    .map(this::convertToResponse)
                    .collect(Collectors.toList());

            LOG.info("Successfully batch inserted {} mappings", mappingsToInsert.size());
            LOG.debug("Converted responses: {}", responses);
        }
        return SuccessCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public BulkEmployeeAttendanceUploadResponse saveEmployeeAttendanceBatchMappings(List<EmployeeEligibilityMappingRequest> requestList, String createdBy) throws DataUpdationException {
        LOG.info("Saving employee attendance batch mappings from UI, total records: {}", requestList.size());

        BulkEmployeeAttendanceUploadResponse response = new BulkEmployeeAttendanceUploadResponse();
        response.setTotalRecords(requestList.size());
        response.setValidationErrors(new ArrayList<>());

        try {
            int successCount = saveEmployeeAttendanceMappingList(requestList, createdBy);

            response.setSuccessfullyProcessed(successCount);
            response.setValidRecords(successCount);
            response.setInvalidRecords(requestList.size() - successCount);
            response.setSuccess(successCount > 0);

            if (successCount == requestList.size()) {
                response.setMessage("All mappings saved successfully!");
            } else if (successCount > 0) {
                response.setMessage(String.format("Partially saved: %d successful, %d failed.", successCount, requestList.size() - successCount));
            } else {
                response.setMessage("Failed to save any mappings due to duplicate entries.");
                response.setSuccess(false);
            }

            LOG.info("Employee Attendance batch save completed: {} successful out of {} total", successCount, requestList.size());

        } catch (Exception e) {
            LOG.error("Error in batch save operation", e);
            response.setSuccess(false);
            response.setMessage("Error occurred during batch save: " + e.getMessage());
            response.setSuccessfullyProcessed(0);
            response.setValidRecords(0);
            response.setInvalidRecords(requestList.size());
            throw new DataUpdationException("Error in batch save operation: " + e.getMessage());
        }

        return response;
    }

    @Override
    @Transactional(readOnly = true)
    public List<EmployeeEligibilityMappingResponse> getEligibilityAttendanceMappingsByEmpId(String empId) {
        try {
            LOG.info("Getting mappings for empId: {}", empId);
            List<EmployeeAttendance> mappings = employeeAttendanceDao.findByEmpId(empId);
            return mappings.stream()
                    .map(this::convertToResponse)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            LOG.error("Error getting mappings for empId: {}", empId, e);
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public View prepareEmployeeMappingExcel(List<Long> empIds) throws Exception {
        try {
            List<EmployeeAttendance> mappings = employeeAttendanceDao.findByEmpIdIn(empIds);

            List<String> headers = Arrays.asList(
                    EmployeeAttendanceMappingColumn.EMPLOYEE_ID.getColumnName(),
                    EmployeeAttendanceMappingColumn.EMPLOYEE_NAME.getColumnName(),
                    EmployeeAttendanceMappingColumn.EMPLOYEE_CODE.getColumnName(),
                    EmployeeAttendanceMappingColumn.MAPPING_TYPE.getColumnName(),
                    EmployeeAttendanceMappingColumn.VALUE.getColumnName(),
                    EmployeeAttendanceMappingColumn.UNIT_NAME.getColumnName(),
                    EmployeeAttendanceMappingColumn.ELIGIBILITY_TYPE.getColumnName(),
                    EmployeeAttendanceMappingColumn.STATUS.getColumnName()
            );

            List<Object[]> rows = new ArrayList<>();

            for (EmployeeAttendance mapping : mappings) {
                Long empId = Long.valueOf(mapping.getEmpId());
                EmployeeBasicDetail employeeDetail = empCache.getEmployee(empId.intValue());
                Map<Integer, Unit> units = masterDataCache.getUnits();

                String empName = employeeDetail != null ? employeeDetail.getName() : "";
                String empCode = employeeDetail != null ? employeeDetail.getEmployeeCode() : "";

                // Get unit name based on mapping type
                String unitName = "";
                if ("UNIT".equals(mapping.getMappingType().name())) {
                    try {
                        Integer unitId = Integer.valueOf(mapping.getValue());
                        Unit unit = units.get(unitId);
                        unitName = unit != null ? unit.getName() : "";
                    } catch (NumberFormatException e) {
                        LOG.warn("Invalid unit ID format: {}", mapping.getValue());
                        unitName = "";
                    }
                } else {
                    // For CITY and REGION mappings, the value is empty
                    unitName = "";
                }

                rows.add(new Object[]{
                        empId,
                        empName,
                        empCode,
                        mapping.getMappingType(),
                        mapping.getValue(),
                        unitName,
                        mapping.getEligibilityType(),
                        mapping.getStatus()
                });
            }

            ExcelRequestData excelRequestData = new ExcelRequestData();
            excelRequestData.setFileName("Employee_Mappings");
            excelRequestData.setHeaderNames(headers);
            excelRequestData.setBody(rows);

            return genericExcelManagementService.downloadExcelFromRequestData(excelRequestData);
        }
        catch (Exception e) {
            LOG.error("Error while preparing employee mapping excel", e);
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public View downloadEmployeeAttendanceBulkUploadTemplate() throws Exception {
        try {
            List<String> headers = Arrays.asList(
                    EmployeeAttendanceMappingColumn.ELIGIBILITY_TYPE.getColumnName(),
                    EmployeeAttendanceMappingColumn.EMPLOYEE_ID.getColumnName(),
                    EmployeeAttendanceMappingColumn.MAPPING_TYPE.getColumnName(),
                    EmployeeAttendanceMappingColumn.STATUS.getColumnName(),
                    EmployeeAttendanceMappingColumn.VALUE.getColumnName()
            );

            List<String[]> rows = Arrays.asList(
                    new String[]{"ATTENDANCE", "EMP001", "UNIT", "ACTIVE", "100000"},
                    new String[]{"APPROVAL", "EMP002", "CITY", "ACTIVE", "Delhi"},
                    new String[]{"ATTENDANCE", "EMP003", "REGION", "ACTIVE", "North"}
            );

            List<Object[]> objectRows = rows.stream()
                    .map(arr -> Arrays.copyOf(arr, arr.length, Object[].class))
                    .collect(Collectors.toList());

            ExcelRequestData excelRequestData = new ExcelRequestData();
            excelRequestData.setFileName("Employee_Eligibility_Mapping_Template");
            excelRequestData.setHeaderNames(headers);
            excelRequestData.setBody(objectRows);

            return genericExcelManagementService.downloadExcelFromRequestData(excelRequestData);
        }
        catch (Exception e) {
            LOG.error("Error while downloading bulk upload template for Employee Attendance Mapping", e);
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public BulkEmployeeAttendanceUploadResponse validateBulkUpload(List<EmployeeEligibilityMappingRequest> bulkData) throws Exception {
        LOG.info("Validating bulk upload with {} records", bulkData.size());
        try {
            BulkEmployeeAttendanceUploadResponse response = new BulkEmployeeAttendanceUploadResponse();
            response.setTotalRecords(bulkData.size());
            response.setValidationErrors(new ArrayList<>());

            List<EmployeeEligibilityMappingRequest> validRecords = new ArrayList<>();
            int rowNumber = 2; // Starting from row 2 (after header)

            // Validate each record
            Set<String> seenKeys = new HashSet<>();
            for (EmployeeEligibilityMappingRequest request : bulkData) {
                List<BulkEmployeeAttendanceUploadResponse.BulkUploadValidationError> recordErrors = validateBulkUploadRecord(request, rowNumber, seenKeys);
                if (recordErrors.isEmpty()) {
                    validRecords.add(request);
                } else {
                    response.getValidationErrors().addAll(recordErrors);
                }
                rowNumber++;
            }

            response.setValidRecords(validRecords.size());
            response.setInvalidRecords(bulkData.size() - validRecords.size());
            response.setSuccessfullyProcessed(0); // No processing done yet
            response.setSuccess(response.getValidationErrors().isEmpty());
            response.setMessage(String.format("Validation completed: %d valid records, %d invalid records found.",
                    validRecords.size(), response.getValidationErrors().size()));

            LOG.info("Bulk validation completed: {} valid, {} invalid", validRecords.size(), response.getValidationErrors().size());
            return response;
        }
        catch (Exception e) {
            LOG.error("Error while validating bulk upload for Employee Attendance Mapping", e);
            throw e;
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public BulkEmployeeAttendanceUploadResponse processBulkUpload(List<EmployeeEligibilityMappingRequest> bulkData, String createdBy) throws Exception {
        LOG.info("Processing bulk upload with {} records", bulkData.size());

        try {
            BulkEmployeeAttendanceUploadResponse response = new BulkEmployeeAttendanceUploadResponse();
            response.setTotalRecords(bulkData.size());
            response.setValidationErrors(new ArrayList<>());

            List<EmployeeEligibilityMappingRequest> validRecords = new ArrayList<>();
            int rowNumber = 2; // Starting from row 2 (after header)

            // Validate each record
            Set<String> seenKeys = new HashSet<>();
            for (EmployeeEligibilityMappingRequest request : bulkData) {
                List<BulkEmployeeAttendanceUploadResponse.BulkUploadValidationError> recordErrors = validateBulkUploadRecord(request, rowNumber ,seenKeys);

                if (recordErrors.isEmpty()) {
                    validRecords.add(request);
                } else {
                    response.getValidationErrors().addAll(recordErrors);
                }
                rowNumber++;
            }

            response.setValidRecords(validRecords.size());
            response.setInvalidRecords(bulkData.size() - validRecords.size());
            int successCount = saveEmployeeAttendanceMappingList(validRecords, createdBy);
            response.setSuccessfullyProcessed(successCount);
            response.setSuccess(successCount > 0);
            response.setMessage(String.format("Processed %d out of %d records successfully. %d validation errors found.",
                    successCount, bulkData.size(), response.getValidationErrors().size()));
            LOG.info("Bulk upload completed for Employee Attendance Mapping: {} successful, {} errors", successCount, response.getValidationErrors().size());
            return response;
        }
        catch (Exception e) {
            LOG.error("Error while processing bulk upload for Employee Attendance Mapping", e);
            throw e;
        }
    }

    private List<BulkEmployeeAttendanceUploadResponse.BulkUploadValidationError> validateBulkUploadRecord(EmployeeEligibilityMappingRequest request, int rowNumber,Set<String> seenKeys) {
        try {
            List<BulkEmployeeAttendanceUploadResponse.BulkUploadValidationError> errors = new ArrayList<>();
            // Check for duplicate rows
            if (isDuplicateRow(request, seenKeys)) {
                errors.add(createValidationError(rowNumber, request.getEmpId(), "value", "Duplicate row in upload file", request.getValue()));
                return errors;
            }

            // Validate eligibility type
            if (request.getEligibilityType() == null) {
                errors.add(createValidationError(rowNumber, request.getEmpId(), "eligibilityType", "Eligibility type is required", ""));
            }

            // Validate employee ID
            if (request.getEmpId() == null || request.getEmpId().trim().isEmpty()) {
                errors.add(createValidationError(rowNumber, request.getEmpId(), "empId", "Employee ID is required", request.getEmpId()));
            }

            // Validate mapping type
            if (request.getMappingType() == null) {
                errors.add(createValidationError(rowNumber, request.getEmpId(), "mappingType", "Mapping type is required", ""));
            }

            // Validate value
            if (request.getValue() == null || request.getValue().trim().isEmpty()) {
                errors.add(createValidationError(rowNumber, request.getEmpId(), "value", "Value is required", request.getValue()));
            }

            // validate status
            if (request.getStatus() == null) {
                errors.add(createValidationError(rowNumber, request.getEmpId(), "status", "Status is required", ""));
            }

            // validation for duplicate mapping
            EmployeeAttendance existingMapping = employeeAttendanceDao.findByEmpIdValueTypeAndStatus(request.getEmpId(),request.getValue(),request.getMappingType(),request.getEligibilityType(),request.getStatus()).stream().findFirst().orElse(null);
            if (existingMapping != null) {
                errors.add(createValidationError(rowNumber, request.getEmpId(), "value", "Mapping already exists", request.getValue()));
            }

            return errors;
        }
        catch (Exception e) {
            LOG.error("Error while validating bulk upload record for Employee Attendance Mapping", e);
            throw e;
        }
    }

    // making function for duplicate rows validation and sending error message
    private boolean isDuplicateRow(EmployeeEligibilityMappingRequest request, Set<String> seenKeys) {
        try {
            String key = String.format("%s|%s|%s|%s|%s",
                    request.getEmpId(),
                    request.getValue(),
                    request.getStatus() != null ? request.getStatus().name() : "",
                    request.getMappingType() != null ? request.getMappingType().name() : "",
                    request.getEligibilityType() != null ? request.getEligibilityType().name() : "");
            if (!seenKeys.contains(key)) {
                seenKeys.add(key);
                return false;
            }

            return seenKeys.contains(key);
        }
        catch (Exception e) {
            LOG.error("Error while checking for duplicate row in bulk upload for Employee Attendance Mapping", e);
            throw e;
        }
    }

    private BulkEmployeeAttendanceUploadResponse.BulkUploadValidationError createValidationError(int rowNumber, String empId, String field, String message, String originalValue) {
        try {
            BulkEmployeeAttendanceUploadResponse.BulkUploadValidationError error = new BulkEmployeeAttendanceUploadResponse.BulkUploadValidationError();
            error.setRowNumber(rowNumber);
            error.setEmpId(empId);
            error.setField(field);
            error.setErrorMessage(message);
            error.setOriginalValue(originalValue);
            return error;
        }
        catch (Exception e) {
            LOG.error("Error while creating validation error for Employee Attendance Mapping", e);
            throw e;
        }
    }



    /**
     * Convert entity to response DTO
     * @param mapping entity
     * @return response DTO
     */
    private EmployeeEligibilityMappingResponse convertToResponse(EmployeeAttendance mapping) {
        EmployeeEligibilityMappingResponse response = new EmployeeEligibilityMappingResponse();
        response.setId(mapping.getId());
        response.setCreatedAt(mapping.getCreatedAt());
        response.setCreatedBy(mapping.getCreatedBy());
        response.setEligibilityType(mapping.getEligibilityType());
        response.setEmpId(mapping.getEmpId());
        response.setMappingType(mapping.getMappingType());
        response.setStatus(mapping.getStatus());
        response.setUpdatedAt(mapping.getUpdatedAt());
        response.setUpdatedBy(mapping.getUpdatedBy());
        response.setValue(mapping.getValue());
        return response;
    }
}
