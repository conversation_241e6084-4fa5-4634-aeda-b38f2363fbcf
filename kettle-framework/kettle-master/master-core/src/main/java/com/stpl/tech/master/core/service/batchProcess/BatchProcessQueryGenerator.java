package com.stpl.tech.master.core.service.batchProcess;

import com.stpl.tech.master.core.exception.MasterException;
import com.stpl.tech.master.core.service.batchProcess.batchAnnotation.BatchProcess;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.Hibernate;
import org.springframework.util.CollectionUtils;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Table;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.EnumMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class BatchProcessQueryGenerator<T> {

    public enum Condition {
        SET_ONLY_FIELDS,
        WHERE_FIELDS
    }

    private final Class<T> entityClass;
    private final List<Field> uploadFields;
    private Field idField;

    public BatchProcessQueryGenerator(Class<T> entityClass) {
        this.entityClass = entityClass;
        this.uploadFields = findBatchProcessFields();
        this.idField = findIdField(entityClass);
    }

    private List<Field> findBatchProcessFields() {
        return Arrays.stream(entityClass.getDeclaredFields())
                .filter(field -> {
                    // Skip static and transient fields
                    if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                            java.lang.reflect.Modifier.isTransient(field.getModifiers())) {
                        return false;
                    }
                    return isValidDatabaseField(field);
                })
                .collect(Collectors.toList());
    }

    private Field findIdField(Class<T> entityClass) {
        Field idField = Arrays.stream(entityClass.getDeclaredFields())
                .filter(f -> f.isAnnotationPresent(Id.class))
                .findFirst()
                .orElse(null);

        if (idField != null) {
            idField.setAccessible(true);
            return idField;
        }

        // If no field with @Id annotation, look for method with @Id annotation
        Optional<Method> idMethod = Optional.empty();

        for(Method method : entityClass.getDeclaredMethods()) {
            if(method.isAnnotationPresent(Id.class)) {
                idMethod = Optional.of(method);
                break;
            }
        }


        if (idMethod.isPresent()) {
            Method method = idMethod.get();
            String methodName = method.getName();
            String fieldName = resolveFieldNameFromMethod(methodName);

            if (fieldName != null) {
                try {
                    Field field = entityClass.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    return field;
                } catch (NoSuchFieldException e) {
                    throw new RuntimeException("Could not find field corresponding to @Id method: " + methodName, e);
                }
            }

            throw new RuntimeException("Could not determine field name from @Id method: " + methodName);
        }

        throw new RuntimeException("No @Id field or method found in " + entityClass.getSimpleName());
    }

    private String findIdColumnName(Class<T> entityClass) {
        Field idField = Arrays.stream(entityClass.getDeclaredFields())
                .filter(f -> f.isAnnotationPresent(Id.class))
                .findFirst()
                .orElse(null);

        if (idField != null) {
            Column columnAnnotation = idField.getAnnotation(Column.class);
            if (columnAnnotation != null) {
                return columnAnnotation.name();
            } else {
                return idField.getName();
            }
        }

        Method idMethod = Arrays.stream(entityClass.getDeclaredMethods())
                .filter(m -> m.isAnnotationPresent(Id.class))
                .findFirst()
                .orElse(null);

        if (idMethod != null) {
            Column columnAnnotation = idMethod.getAnnotation(Column.class);
            if (columnAnnotation != null) {
                return columnAnnotation.name();
            } else {
                return idMethod.getName();
            }
        }
        throw new RuntimeException("No @Id field or method found in " + entityClass.getSimpleName());
    }

    private String resolveFieldNameFromMethod(String methodName) {
        if (methodName.startsWith("get") && methodName.length() > 3) {
            return methodName.substring(3, 4).toLowerCase() + methodName.substring(4);
        }
        else if (methodName.startsWith("is") && methodName.length() > 2) {
            return methodName.substring(2, 3).toLowerCase() + methodName.substring(3);
        }
        return null;
    }

    private boolean isValidDatabaseField(Field field) {
        // Check @Column on field
        Column columnAnnotation = field.getAnnotation(Column.class);
        if (columnAnnotation != null) {
            return columnAnnotation.insertable();
        }

        // Check @JoinColumn on field
        JoinColumn joinColumnAnnotation = field.getAnnotation(JoinColumn.class);
        if (joinColumnAnnotation != null) {
            return joinColumnAnnotation.insertable();
        }

        // Build standard getter method name
        String getterMethodName = "get" + capitalize(field.getName());

        try {
            Method getterMethod = entityClass.getMethod(getterMethodName);

            // Check @Column on getter
            Column getterColumnAnnotation = getterMethod.getAnnotation(Column.class);
            if (getterColumnAnnotation != null) {
                return getterColumnAnnotation.insertable();
            }

            // Check @JoinColumn on getter
            JoinColumn getterJoinColumnAnnotation = getterMethod.getAnnotation(JoinColumn.class);
            if (getterJoinColumnAnnotation != null) {
                return getterJoinColumnAnnotation.insertable();
            }
        } catch (NoSuchMethodException e) {
            // Try boolean getter if applicable
            if (field.getType() == boolean.class || field.getType() == Boolean.class) {
                String booleanGetterMethodName = "is" + capitalize(field.getName());
                try {
                    Method booleanGetterMethod = entityClass.getMethod(booleanGetterMethodName);

                    Column booleanGetterColumnAnnotation = booleanGetterMethod.getAnnotation(Column.class);
                    if (booleanGetterColumnAnnotation != null) {
                        return booleanGetterColumnAnnotation.insertable();
                    }

                    JoinColumn booleanGetterJoinColumnAnnotation = booleanGetterMethod.getAnnotation(JoinColumn.class);
                    if (booleanGetterJoinColumnAnnotation != null) {
                        return booleanGetterJoinColumnAnnotation.insertable();
                    }
                } catch (NoSuchMethodException ex) {
                    log.error("Getter method not found for field: {}. Assuming it's a simple type. : {}", field.getName(), ex.getMessage());
                }
            }
        }

        // Fallback: treat it as valid if it's a simple type
        return isSimpleType(field.getType());
    }

    private String capitalize(String name) {
        return name.substring(0, 1).toUpperCase() + name.substring(1);
    }


    private boolean isSimpleType(Class<?> type) {
        return type.isPrimitive() ||
                Number.class.isAssignableFrom(type) ||
                String.class.isAssignableFrom(type) ||
                Date.class.isAssignableFrom(type) ||
                Boolean.class.isAssignableFrom(type) ||
                type.isEnum();
    }

    public String generateInsertQuery() {
        BatchProcess batchProcess = entityClass.getAnnotation(BatchProcess.class);
        String tableName = determineTableName(batchProcess);

        String ID_COLUMN_NAME = findIdColumnName(entityClass);

        List<String> columnNames = getColumnNames();

        columnNames.remove(ID_COLUMN_NAME);

        return String.format(
                "INSERT INTO %s (%s)",
                tableName,
                String.join(", ", columnNames)
        );
    }

    public List<String> getColumnNames() {
        return uploadFields.stream()
                .filter(field -> !field.equals(idField))
                .map(this::getColumnName)
                .collect(Collectors.toList());
    }

    public String generateUpdateQuery(EnumMap<Condition, Set<String>> conditions) {
        BatchProcess batchProcess = entityClass.getAnnotation(BatchProcess.class);
        String tableName = determineTableName(batchProcess);

        Set<String> columnsToUpdate = CollectionUtils.isEmpty(conditions) ? null : conditions.get(Condition.SET_ONLY_FIELDS);

        List<Field> fieldsToUpdate = uploadFields.stream()
                .filter(field -> !field.equals(idField))
                .filter(field -> columnsToUpdate == null || columnsToUpdate.contains(field.getName()))
                .collect(Collectors.toList());

        String setClause = fieldsToUpdate.stream()
                .map(field -> getColumnName(field) + " = ?")
                .collect(Collectors.joining(", "));

        return String.format(
                "UPDATE %s SET %s WHERE %s = ? ",
                tableName,
                setClause,
                getColumnName(idField)
        );
    }

    private String determineTableName(BatchProcess batchProcess) {
        if (batchProcess != null && !batchProcess.tableName().isBlank()) {
            return batchProcess.tableName();
        }
        Table tableAnnotation = entityClass.getAnnotation(Table.class);
        if (tableAnnotation != null && !tableAnnotation.name().isEmpty()) {
            return tableAnnotation.name();
        }

        // Fallback to camelToSnakeCase of class name
        return camelToSnakeCase(entityClass.getSimpleName());
    }

    private String getColumnName(Field field) {
        // Check for @Column annotation
        Column columnAnnotation = field.getAnnotation(Column.class);
        if (columnAnnotation != null && !columnAnnotation.name().isEmpty()) {
            return columnAnnotation.name();
        }

        // Check for @JoinColumn annotation
        JoinColumn joinColumnAnnotation = field.getAnnotation(JoinColumn.class);
        if (joinColumnAnnotation != null && !joinColumnAnnotation.name().isEmpty()) {
            return joinColumnAnnotation.name();
        }

        // Check corresponding getter for @Column
        Method getterMethod = findGetterMethod(field);
        if (getterMethod != null) {
            Column getterColumnAnnotation = getterMethod.getAnnotation(Column.class);
            if (getterColumnAnnotation != null && !getterColumnAnnotation.name().isEmpty()) {
                return getterColumnAnnotation.name();
            }

            // Check corresponding getter for @JoinColumn
            JoinColumn getterJoinColumnAnnotation = getterMethod.getAnnotation(JoinColumn.class);
            if (getterJoinColumnAnnotation != null && !getterJoinColumnAnnotation.name().isEmpty()) {
                return getterJoinColumnAnnotation.name();
            }
        }

        // Fallback to snake_case
        return camelToSnakeCase(field.getName());
    }


    private Method findGetterMethod(Field field) {
        String fieldName = field.getName();
        String standardGetterName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);

        try {
            return entityClass.getMethod(standardGetterName);
        } catch (NoSuchMethodException e) {
            // Try boolean-style getter for boolean fields
            if (field.getType() == boolean.class || field.getType() == Boolean.class) {
                String booleanGetterName = "is" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                try {
                    return entityClass.getMethod(booleanGetterName);
                } catch (NoSuchMethodException ex) {
                    // No getter method found
                    return null;
                }
            }

            // Search for getter method that doesn't follow standard naming convention
            return Arrays.stream(entityClass.getMethods())
                    .filter(method -> method.isAnnotationPresent(Column.class) &&
                            method.getParameterCount() == 0 &&
                            (method.getName().startsWith("get") || method.getName().startsWith("is")))
                    .filter(method -> {
                        String methodFieldName = resolveFieldNameFromMethod(method.getName());
                        return methodFieldName != null && methodFieldName.equals(fieldName);
                    })
                    .findFirst()
                    .orElse(null);
        }
    }

    private String camelToSnakeCase(String input) {
        return input.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }

    public List<Object> mapEntityToStatement(T entity) throws MasterException {
        List<Object> values = new ArrayList<>();
        try {
            for (Field field : uploadFields) {
                if (field.equals(idField)) {
                    continue;
                }
                field.setAccessible(true);
                Object value = field.get(entity);
                if (value == null) {
                    values.add(null);
                    continue;
                }
                Class<?> fieldType = field.getType();
                if (fieldType.isEnum()) {
                    values.add(((Enum<?>) value).name());  // Convert Enum to String
                } else if (!isSimpleType(value.getClass())) {
                    Object foreignKeyValue = extractIdValue(value);
                    values.add(foreignKeyValue);
                } else {
                    values.add(value);
                }
            }
        } catch (IllegalAccessException e) {
            throw new MasterException("Failed to extract values from entity", e);
        }
        return values;
    }

    private Object extractIdValue(Object foreignEntity) throws IllegalAccessException {
        Object entity = Hibernate.unproxy(foreignEntity);
        Field field = findIdField((Class<T>) entity.getClass());
        return field.get(entity);
    }


    public void mapEntityToStatementForUpdate(T entity, PreparedStatement ps, EnumMap<Condition, Set<String>> conditions) throws SQLException, MasterException {
        try {
            int index = 1;
            Set<String> columnsToUpdate = CollectionUtils.isEmpty(conditions) ? null : conditions.get(Condition.SET_ONLY_FIELDS);
            for (Field field : uploadFields) {
                if (field.equals(idField)) {
                    continue;
                }
                if (columnsToUpdate != null && !columnsToUpdate.contains(field.getName())) {
                    continue;
                }

                field.setAccessible(true);
                Object value = field.get(entity);
                if (value != null && !isSimpleType(value.getClass())) {
                    Object foreignKeyValue = extractIdValue(value);
                    ps.setObject(index++, foreignKeyValue);
                } else {
                    ps.setObject(index++, value);
                }
            }

            idField.setAccessible(true);
            ps.setObject(index, idField.get(entity));

        } catch (IllegalAccessException e) {
            throw new MasterException("Failed to map entity to statement for update", e);
        }
    }
}