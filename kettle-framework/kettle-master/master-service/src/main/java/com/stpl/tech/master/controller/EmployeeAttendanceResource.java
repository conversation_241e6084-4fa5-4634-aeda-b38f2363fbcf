package com.stpl.tech.master.controller;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.EmployeeAttendanceService;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingRequest;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingResponse;
import com.stpl.tech.master.domain.model.BulkEmployeeAttendanceUploadResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.EMPLOYEE_ATTENDANCE_ROOT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;
import static com.stpl.tech.master.service.core.MasterServiceConstants.USER_MANAGEMENT_SERVICES_ROOT_CONTEXT;

/**
 * REST Controller for Employee Eligibility Mapping operations
 */
@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + USER_MANAGEMENT_SERVICES_ROOT_CONTEXT +  SEPARATOR + EMPLOYEE_ATTENDANCE_ROOT_CONTEXT)
public class EmployeeAttendanceResource extends AbstractResources  {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeAttendanceResource.class);

    @Autowired
    private EmployeeAttendanceService employeeAttendanceService;


    @PostMapping(value = "/mappings", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BulkEmployeeAttendanceUploadResponse saveMappingsBatch(@Valid @RequestBody List<EmployeeEligibilityMappingRequest> mappingData,
                                                                  HttpServletRequest httpRequest) throws DataUpdationException {
        LOG.info("Request to save employee attendance batch mappings with {} records", mappingData.size());
        return employeeAttendanceService.saveEmployeeAttendanceBatchMappings(mappingData, String.valueOf(getLoggedInUser(httpRequest)));
    }

    @GetMapping(value = "/mappings/{empId}", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    public List<EmployeeEligibilityMappingResponse> getAttendanceMappingsByEmpId(@PathVariable("empId") String empId) {
        LOG.info("Request to get attendance mappings for empId: {}", empId);
        return employeeAttendanceService.getEligibilityAttendanceMappingsByEmpId(empId);
    }

    @PostMapping(value = "/export-mappings-excel" ,consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE )
    public View downloadMappingsExcel(@RequestBody List<Long> empIds) throws Exception {
        return employeeAttendanceService.prepareEmployeeMappingExcel(empIds);
    }

    @GetMapping("/download-template")
    public View downloadBulkUploadTemplate() throws Exception {
        LOG.info("Request to download bulk upload template for Employee Attendance Mapping");
        return employeeAttendanceService.downloadEmployeeAttendanceBulkUploadTemplate();
    }

    @PostMapping(value = "/validate-bulk-upload", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BulkEmployeeAttendanceUploadResponse validateBulkUpload(@Valid @RequestBody List<EmployeeEligibilityMappingRequest> bulkData) throws Exception {
        LOG.info("Request to validate bulk upload for Employee Attendance Mapping with {} records", bulkData.size());
        return employeeAttendanceService.validateBulkUpload(bulkData);
    }

    @PostMapping(value = "/bulk-upload-mappings", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BulkEmployeeAttendanceUploadResponse processBulkUpload(@Valid @RequestBody List<EmployeeEligibilityMappingRequest> bulkData,
                                                                  HttpServletRequest httpRequest) throws Exception {
        LOG.info("Request to process bulk upload for Employee Attendance Mapping with {} records", bulkData.size());
        return employeeAttendanceService.processBulkUpload(bulkData, String.valueOf(getLoggedInUser(httpRequest)));
    }

}

