/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

adminapp.controller("MainController", function ($rootScope, $scope, $location, $http,
                                                $cookieStore, AuthService, AppUtil, $window) {

    $scope.init = function () {
        $scope.getCurrentUser = AppUtil.getCurrentUser();
        $scope.showToAdmin = AppUtil.checkPermission('master-service.*');
        $scope.hasUsrMgtPrivilege = AppUtil.checkPermission("master-service.user-management.*");
        $scope.hasOfferPrivilege = AppUtil.checkPermission("master-service.offer-management.*");
        $scope.hasAccountsPrivilege = AppUtil.checkPermission("kettle-service.cash-management.*");
        $scope.hasCheckListPrivilege = AppUtil.checkPermission("kettle-checklist.checklist-management.*");
        $scope.hasUnitMgtPrivilege = AppUtil.checkPermission("master-service.unit-metadata.*");
        $scope.hasProductMgtPrivilege = AppUtil.checkPermission("master-service.product-metadata.*");
        $scope.hasRecipePrivilege = AppUtil.checkPermission("master-service.recipe.*");
        $scope.hasAddRecipePrivilege = AppUtil.checkPermission("master-service.recipe.add");
        $scope.hasMonkUnitMgtPrivilege = AppUtil.checkPermission("master-service.kiosk-management.*");
        $scope.hasReportingPrivilege = AppUtil.checkPermission("kettle-service.report-metadata.*");
        $scope.hasBRMPrivilege = AppUtil.checkPermission("kettle-service.report-metadata.report.*");
        $scope.hasUnitMappingMPrivilege = AppUtil.checkPermission("master-service.unit-metadata.*");
        $scope.hasUnitAuditPrivilege = AppUtil.checkPermission("kettle-service.pos-metadata.audit.*");
        $scope.hasTaxMgtPrivilege = AppUtil.checkPermission("master-service.tax-metadata.*");
        $scope.hasWebAppPrivilege = AppUtil.checkPermission("neo-service.ncm.*");
        $scope.budgetManager = AppUtil.checkPermission("kettle-service.budget-metadata.*");
        $scope.companyName = $window.localStorage.getItem('loggedInCompanyName');
        $scope.brandName = $window.localStorage.getItem('loggedInBrandName');
        $scope.aclData = $rootScope.aclData;
        $rootScope.supAdmnFilter = $scope.aclData.action['SUP_ADMN'] ? false : true;
        if ($window.localStorage.getItem('supAdmnFilter') == "false") {
            $rootScope.supAdmnFilter = false;
        } else if ($window.localStorage.getItem('supAdmnFilter') == "true") {
            $rootScope.supAdmnFilter = true;
        }
    };

    $scope.toggleBrandFilter = function () {
        $rootScope.supAdmnFilter = !$rootScope.supAdmnFilter;
        $window.localStorage.setItem('supAdmnFilter', $rootScope.supAdmnFilter);
        $window.location.reload();
    }

    if ($location.path() == "/dashboard") {
        $location.path("dashboard/unit");
    }

    $scope.showCafeDashboard = function () {
        $scope.loading = true;
        $location.path("dashboard/unit/CAFE");
    };


    $scope.goToMonkConfiguration = function () {
        $location.path("dashboard/monkConf");
    };

    $scope.showProductDetails = function(){
    	$location.path("dashboard/productDetails");
    };

    $scope.showProductDetail = function() {
        $location.path("dashboard/productDetail")
    }

    $scope.showCODDashboard = function () {
        $scope.loading = true;
        $location.path("dashboard/unit/DELIVERY");
    };

    $scope.showChaiMonkDashboard = function () {
        $scope.loading = true;
        $location.path("dashboard/unit/CHAI_MONK");
    };

    $scope.showTestingUnitDashboard = function () {
        $scope.loading = true;
        $location.path("dashboard/unit/TESTING_UNIT");
    };

    $scope.showKitchenDashboard = function () {
        $scope.loading = true;
        $location.path("dashboard/unit/KITCHEN");
    };

    $scope.showWareHouseDashboard = function () {
        $scope.loading = true;
        $location.path("dashboard/unit/WAREHOUSE");
    };

    $scope.showOfficeDashboard = function () {
        $scope.loading = true;
        $location.path("dashboard/unit/OFFICE");
    };

    $scope.showEmployeeMealDashboard = function() {
		$scope.loading = true;
		$location.path("dashboard/unit/EMPLOYEE_MEAL");
	};

    $scope.refer = function () {
        $scope.loading = true;
        $location.path("dashboard/refer");
    };

    $scope.showCallCenterDashboard = function () {
        $scope.loading = true;
        $location.path("dashboard/unit/COD");

    };
    $scope.showEmployeeDashboard = function () {
        $scope.loading = true;
        $location.path("dashboard/employee");
    };

    $scope.showEmployeeAttendanceMapping = function () {
        $scope.loading = true;
        $location.path("dashboard/employeeEligibilityMapping");
    };

    $scope.showTakeawayDashboard = function () {
        $scope.loading = true;
        $location.path("dashboard/unit/TAKE_AWAY");
    };

    $scope.showCategoryList = function () {
        $location.path("dashboard/categoryList");
    };

    $scope.showCreditAccounts = function () {
        $location.path("dashboard/creditAccounts");
    };

    $scope.showCategoryData = function () {
        $location.path("dashboard/categoryData");
    };

    $scope.showCampaignBuilder = function() {
        $location.path("dashboard/campaignBuilder");
    };

    $scope.showCampaignDetails = function() {
        $location.path("dashboard/campaignDetailsView")
    };

    $scope.showDeliveryCouponUploader = function() {
        $location.path("dashboard/deliveryCouponUpload")
    };

        $scope.showDroolForOffer = function() {
            $location.path("dashboard/droolForOffer")
        };

    $scope.showProductsDetail = function () {
        $location.path("dashboard/productData");
    };

    $scope.showProductRecipeMapping = function () {
        $location.path("dashboard/productRecipeMapping");
    };
    
    $scope.showProductRecipeMappingOld = function () {
            $location.path("dashboard/productDetailsProfile");
        };

    $scope.showPriceProfileProductMapping = function () {
            $location.path("dashboard/priceProfileProductMapping");
    };

    $scope.showPriceProfileCreation = function () {
        $location.path("dashboard/priceProfileCreation");
    };

    $scope.showUnitPriceProfile  =function(){
        $location.path("dashboard/unitPriceProfile")
    }


    $scope.showUPTCalculations =function () {
        $location.path("dashboard/UPTCalculations");
    };

    $scope.showExceptionDays =function () {
        $location.path("dashboard/exceptionDay");
    };

    $scope.showDeliveryMapping = function () {
        $location.path("dashboard/deliveryMapping");
    };
    $scope.showUnitClosureDetails = function () {
        $location.path("dashboard/unitClosureDetail");
    };

    $scope.showDeliveryWiseMappingDetails = function () {
        $location.path("dashboard/unitDeliveryMapping");
    };

    $scope.showUnitContactsMappingDetails = function () {
        $location.path("dashboard/unitContactsMapping");
    };

    $scope.showViewRegionMappings = function () {
        $location.path("dashboard/viewRegionMappings");
    };

    $scope.showPaymentModeMapping = function () {
        $location.path("dashboard/paymentModeMapping");
    };

    $scope.updatePaymentModeMapping= function () {
        $location.path("dashboard/updatePaymentModeMapping");
    };

    $scope.showUnitPartnerBrandMetadataDetail = function () {
        $location.path("dashboard/unitPartnerBrandMetadata");
    };

    $scope.addLocation = function () {
        $location.path("dashboard/addLocation");
    };

    $scope.showProductPrize = function () {
        $location.path("dashboard/priceList");
    };

    $scope.showOffersDetails = function () {
        $location.path("dashboard/offerData");
    };

    $scope.showCoupons = function () {
        $location.path("dashboard/couponsData");
    };

    $scope.showPartners = function () {
        $location.path("dashboard/partnerList");
    };

    $scope.showWebTags = function () {
        $location.path("dashboard/productWebTags");
    };

    $scope.showWebLocalities = function () {
        $location.path("dashboard/webappLocalities");
    };

    $scope.viewAuditReport = function () {
        $location.path("dashboard/auditReport");
    };
    $scope.viewManualBillReport = function () {
        $location.path("dashboard/manualBillReport");
    };
    $scope.viewStockReport = function () {
        $location.path("dashboard/stockReport");
    };
    $scope.showReportsDashboard = function () {
        $location.path("dashboard/reports");
    };
    $scope.showBrandReportsDashboard = function () {
        $location.path("dashboard/brandReports");
    };
    $scope.showReportsSummary = function () {
        $location.path("dashboard/reportSummary");
    };
    $scope.showEmployeeBenefit = function () {
        $location.path("dashboard/employeeBenefit");
    };
    $scope.activateCashCards = function () {
        $location.path("dashboard/activateCashCards");
    };

    $scope.kettleCache = function () {
        $location.path("dashboard/kettleCache");
    };

    $scope.showCashSettlementsView = function () {
        $location.path("dashboard/cashSettlementsView").search({
            searchByUnitId: true
        });
        $rootScope.$broadcast('cashSettlementView', {
            searchByUnitId: true
        });
    };

    $scope.showCashSettlementsViewByType = function () {
        $location.path("dashboard/cashSettlementsView").search({
            searchByUnitId: false
        });
        $rootScope.$broadcast('cashSettlementView', {
            searchByUnitId: false
        });
    };

    $scope.showCashSettlementsClose = function () {
        $location.path("dashboard/cashSettlementsClose").search({
            searchByUnitId: true
        });
        $rootScope.$broadcast('cashSettlement', {
            searchByUnitId: true
        });
    };

    $scope.showCashSettlementsCloseByType = function () {
        $location.path("dashboard/cashSettlementsClose").search({
            searchByUnitId: false
        });
        $rootScope.$broadcast('cashSettlement', {
            searchByUnitId: false
        });
    };

    $scope.addNewRecipe = function () {
        $location.path("dashboard/addNewRecipe");
    };

    $scope.addNewRecipeMedia = function () {
        $location.path("dashboard/addNewRecipeMedia");
    };

    $scope.uploadRecipeCondiment = function () {
        $location.path("dashboard/uploadRecipeCondiment");
    };

    $scope.addMonkRecipe = function () {
        $location.path("dashboard/addMonkRecipe");
    };

    $scope.manageMonkRecipe  = function () {
        $location.path("dashboard/manageMonkRecipe");
    };

    $scope.uploadMonkRecipe  = function () {
        $location.path("dashboard/uploadMonkRecipe");
    };

    $scope.SCMRecipeCalculator = function() {
    	$location.path("dashboard/SCMRecipeCalculator");
    };

    $scope.findRecipe = function () {
        $location.path("dashboard/findRecipe");
    };

    $scope.addNewSCMRecipe = function () {
        $location.path("dashboard/addNewSCMRecipe");
    };

    $scope.findSCMRecipe = function () {
        $location.path("dashboard/findSCMRecipe");
    };

    $scope.showCheckListManage = function () {
        $location.path("dashboard/checkList");
    };

    $scope.showBrmView = function () {
        $location.path("dashboard/brm");
    };

    $scope.showBrmViewUnitWise = function(){
        $location.path("dashboard/brmUnitWise")
    };

    $scope.unitReportMapping = function(){
        $location.path("dashboard/unitReportMapping");
    };

    $scope.showAccessList = function () {
        $location.path("dashboard/accessList");
    };

    $scope.showPreAuthApiMgt = function () {
        $location.path("dashboard/preAuthenticatedApiMgt");
    };

    $scope.showEmployeePermissionMappingList = function () {
        $location.path("dashboard/employePermissionMapping");
    };

    $scope.showUserRolePolicy = function () {
        $location.path("dashboard/userPolicyManagement");
    };

    $scope.showEmployeeRolePolicyReset = function () {
        $location.path("dashboard/employeeRolePolicyReset");
    };

    $scope.showOfficeBoard = function () {
        $scope.loading = true;
        $location.path("dashboard/kiosOffice");
    };

    $scope.showCompanyBoard = function () {
        $scope.loading = true;
        $location.path("dashboard/kiosCompany");
    };

    $scope.showCustomerManagment = function () {
        $scope.loading = true;
        $location.path("dashboard/customerDetail");
    };

    $scope.showCustomerFaceManagement = function () {
        $scope.loading = true;
        $location.path("dashboard/customerFaceRecognition");
    };

    $scope.showLocationBoard = function () {
        $scope.loading = true;
        $location.path("dashboard/kiosLocation");
    };

    $scope.showDeliveryMappings = function () {
        $location.path("dashboard/deliveryMappings");
    };
    $scope.showPartnerEdcMapping = function () {
        $location.path("dashboard/partnerEdcMapping");
    };
    $scope.showTaxCategoryView = function () {
        $location.path("dashboard/taxCategory");
    };

    $scope.showTaxMappingView = function () {
        $location.path("dashboard/taxMappings");
    };

    $scope.showBudgetUploderView = function () {
        $location.path("dashboard/budgetUploader");
    };

    $scope.showUpdateBudget = function () {
        $location.path("dashboard/budgetUpdate");
    };

    $scope.showBudgetManpower = function () {
        $location.path("dashboard/budgetUpdateManpower");
    };

    $scope.showBudgetChannelPartner = function () {
        $location.path("dashboard/budgetUpdateChannelPartner");
    };

    $scope.showBudgetFacilityCharges = function () {
        $location.path("dashboard/budgetUpdateFacilityCharges");
    };

    $scope.showBudgetBankCharges = function () {
        $location.path("dashboard/budgetUpdateBankCharges");
    };

    $scope.showBudgetServiceCharges = function () {
        $location.path("dashboard/budgetUpdateServiceCharges");
    };

    $scope.showBudgetPenetrationTargets = function () {
        $location.path("dashboard/penetrationTargets");
    };

    $scope.showBudgetRegeneratePnL = function () {
        $location.path("dashboard/regeneratePnL");
    };

    $scope.showBudgetRegeneratePnLAll = function () {
        $location.path("dashboard/regeneratePnLAll");
    };

    $scope.showBudgetRegenerateFinalizedPnL = function () {
        $location.path("dashboard/regenerateFinalizedPnL");
    };

    $scope.showBudgetRegenerateClosedPnL = function () {
        $location.path("dashboard/regenerateClosedPnL");
    };

    $scope.showPnLReport = function () {
        $location.path("dashboard/PnLReport");
    };

    $scope.showPnLReportDownload = function () {
        $location.path("dashboard/PnLReportDownload");
    };

    $scope.showDSRUploderView = function () {
        $location.path("dashboard/dsrUploader");
    };

    $scope.showDSRConfigurationView=function (){
        $location.path("dashboard/dsrConfiguration")
    };

    $scope.showPartnerUnitMappingView = function () {
        $location.path("dashboard/partnerUnitMapping");
    };

    $scope.showMenuAuditHistory = function () {
        $location.path("dashboard/menuAuditHistory");
    };

    $scope.showPartnerView = function () {
        $location.path("dashboard/partnerManagement");
    };

    $scope.showPartnerMenuView = function () {
        $location.path("dashboard/partnerMenuManagement");
    };

    $scope.showPartnerCategoryView = function () {
        $location.path("dashboard/partnerCategoryManagement");
    };

    $scope.showDesiChaiProfilesView = function() {
        $location.path("dashboard/desiChaiCustomProfiles");
    };

    $scope.showPartnerProductStockView = function () {
        $location.path("dashboard/partnerProductStock");
    };

    $scope.showPartnerCafeStatusHistory = function () {
        $location.path("dashboard/partnerCafeStatusHistory");
    };

    $scope.showDeveloperDashboard = function () {
        $location.path("dashboard/developerDashboard");
    };

    $scope.showReferralView = function () {
        $location.path("dashboard/referral");
    };

    $scope.showAppOffersDetails = function () {
        $location.path("dashboard/appOfferData");
    };
    $scope.showAppSectionDetails=function (){
        $location.path("dashboard/appSectionData");
    };

    $scope.showCRMAppBannerDetails=function (){
        $location.path("dashboard/CRMAppBanner");
    };

    $scope.showCouponDetails=function (){
        $location.path("dashboard/signupCouponDetails");
    };

    $scope.showAppNotification = function () {
        $location.path("dashboard/appNotification");
    };

    $scope.showBanners = function () {
        $location.path("dashboard/banner");
    };

    $scope.showKettleLocalities = function () {
        $location.path("dashboard/kettleLocalities");
    };

    $scope.showHistoryMapping = function () {
        $location.path("dashboard/historyData");
    };

    $scope.showPriceProfile = function () {
        $location.path("dashboard/priceProfile");
    };

    $scope.showProductPriceBulkUpdate = function () {
        $location.path("dashboard/productPriceBulkUpdate")
    };

    $scope.showProductCheckList = function () {
        $location.path("dashboard/productCheckList");
    };

    $scope.showOrderManagement = function () {
        $location.path("dashboard/orderManagement")
    };

    $scope.showUploadCustomer = function () {
        $location.path("dashboard/uploadCustomer")
    };

    $scope.dataEncryptionDashboard = function () {
        $location.path("dashboard/dataEncryptionDashboard")
    };

     $scope.monkRealTimeDashboard = function () {
            $location.path("dashboard/monkRealTimeDashboard")
        };

    $scope.manageB2BMonkCustomers = function () {
        $location.path("dashboard/manageB2BMonkCustomers")
    };

    $scope.viewMonkOrdersAndActions = function () {
        $location.path("dashboard/viewMonkOrdersAndActions")
    };

    $scope.restartAppCache = function () {
        $location.path("dashboard/dineInAppRestartDashboard")
    };

    $scope.showRedeemedCouponUploader = function() {
        $location.path("dashboard/redeemedCouponUpload")
    };

    $scope.showCashbackOfferView = function () {
        $location.path("dashboard/cashbackOffer")
    };

    $scope.showUnitVersionManagementNew = function () {
            $location.path("dashboard/versionManagementNew")
    };

    $scope.showVersionDetails = function () {
        $location.path("dashboard/versionDetails")
    };

    $scope.showUnitVersionDetail = function () {
        $location.path("dashboard/unitVersionDetail")
    };

    $scope.showBrandAttributes = function () {
        $location.path("dashboard/brandAttributes")
    };

    $scope.showFeedbackQuestions = function () {
        $location.path("dashboard/feedbackQuestions")
    };

    $scope.showLoyaltyManagement = function () {
        $location.path("dashboard/loyaltyManagement")
    };

    $scope.showOrderPaymentManagement = function () {
        $location.path("dashboard/orderPaymentManagement")
    };


    $scope.showGenerateWinbackCoupon = function () {
         $location.path("dashboard/generateWinbackCoupon")
    };

    $scope.showLookupWinbackCoupon = function(){
         $location.path("dashboard/lookupWinbackCoupon")
    }

    $scope.showPartnerDqrMapping = function(){
        $location.path("dashboard/partnerDqrMapping")
    }

    $scope.showUnitProductPriceSync = function(){
        $location.path("dashboard/unitProductPriceSync")
    }

    $scope.assemblyNotification = function(){
     $location.path("dashboard/assemblyNotification")
    }

    $scope.showUnitDroolMapping = function(){
       $location.path("dashboard/unitDroolMapping")
    }

    $scope.showLCDMenuFileUpload = function(){
        $location.path("dashboard/lcdMenuFileUpload")
     }



        $scope.logout = function () {
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'POST',
                url: AppUtil.restUrls.users.logout,
            data:AppUtil.getUserValues(),
            }).then(function success(response) {
                $rootScope.showFullScreenLoader = false;
                localStorage.removeItem('userValues');
                localStorage.removeItem('loggedInCompanyId');
                localStorage.removeItem('loggedInBrandId');
                localStorage.removeItem('loggedInCompanyName');
                localStorage.removeItem('loggedInBrandName');
                localStorage.clear();
                AuthService.setAuthorization(null);
                $location.path("/login");
            });
    };

    $scope.goToMonk2Configuration = function () {
        $location.path("dashboard/monk2Conf");
    };


});
